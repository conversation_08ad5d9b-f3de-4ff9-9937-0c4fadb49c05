import Swal from 'sweetalert2';

type OrderItem = {
	productId: string;
	quantity: number;
	price: number;
	size?: string;
	color?: string;
};

type OrderData = {
	customer: {
		name: string;
		phone: string;
		address: string;
	};
	items: OrderItem[];
	total: number;
};

export const OrderService = {
	async submitOrder(orderData: OrderData) {
		try {
			// Replace with your actual API endpoint
			const response = await fetch('/api/orders', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(orderData)
			});

			if (!response.ok) {
				throw new Error('Failed to submit order');
			}

			await Swal.fire({
				title: 'Order Complete!',
				text: 'Your order has been placed successfully',
				icon: 'success',
				confirmButtonColor: '#1eb854'
			});

			if (typeof window !== 'undefined') {
				sessionStorage.setItem(
					'lastOrder',
					JSON.stringify({
						...orderData,
						orderId: response.orderId,
						date: new Date().toISOString()
					})
				);
			}

			return await response.json();
		} catch (error) {
			await Swal.fire({
				title: 'Error!',
				text: error.message || 'Failed to submit order',
				icon: 'error',
				confirmButtonColor: '#f87272'
			});
			throw error;
		}
	}
};
