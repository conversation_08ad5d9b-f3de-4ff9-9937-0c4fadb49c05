<script lang="ts">
	import { onMount } from 'svelte';
	import { cart } from '$lib/stores/cart';
	import { CartService } from '$lib/services/cart.service';
	import { FontAwesomeIcon } from '@fortawesome/svelte-fontawesome';
	import { faSearchPlus, faChevronLeft } from '@fortawesome/free-solid-svg-icons';

	export let data;

	let selectedImage: string;
	let showZoomModal = false;
	let zoomImage = '';
	let selectedSize = '';
	let selectedColor = '';
	let quantity: number = 1;

	onMount(() => {
		selectedImage = data.product.images[0];
	});

	function zoomImageClick(image: string) {
		zoomImage = image;
		showZoomModal = true;
		document.body.style.overflow = 'hidden';
	}

	function closeModal() {
		showZoomModal = false;
		document.body.style.overflow = 'auto';
	}

	async function addToCart() {

		if (data.product.product_type === 'single') {
			await CartService.addItem(data.product, {
				size: 'default',
				color: 'default',
				quantity: quantity
			});
			return
		}

		await CartService.addItem(data.product, {
			size: selectedSize,
			color: selectedColor,
			quantity: quantity
		});
	}
</script>

<div class="container mx-auto px-4 py-8 max-w-7xl">
	<!-- Back Button -->
	<a href="/" class="btn btn-ghost mb-6">
		<FontAwesomeIcon icon={faChevronLeft} class="mr-2" />
		Back to Products
	</a>

	<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
		<!-- Image Gallery -->
		<div>
			<!-- Main Image -->
			<div class="relative mb-4 rounded-lg overflow-hidden bg-base-200 aspect-square">
				<button
					type="button"
					class="w-full h-full p-0 border-0 bg-transparent cursor-zoom-in"
					on:click={() => zoomImageClick(selectedImage)}
				>
					<img
						src={selectedImage}
						alt={data.product.title}
						class="w-full h-full object-contain"
					/>
				</button>
				<button
					class="absolute bottom-4 right-4 btn btn-circle btn-sm btn-primary"
					on:click={() => zoomImageClick(selectedImage)}
				>
					<FontAwesomeIcon icon={faSearchPlus} />
				</button>
			</div>

			<!-- Thumbnails -->
			<div class="grid grid-cols-4 gap-2">
				{#each data.product.images as image, index}
					<button
						class={`rounded-lg overflow-hidden aspect-square ${selectedImage === image ? 'ring-2 ring-primary' : ''}`}
						on:click={() => (selectedImage = image)}
					>
						<img src={image} alt={`Thumbnail ${index + 1}`} class="w-full h-full object-cover" />
					</button>
				{/each}
			</div>
		</div>

		<!-- Product Info -->
		<div>
			<h1 class="text-3xl font-bold mb-2">{data.product.name}</h1>
			<div class="badge badge-secondary mb-4">{data.product.item_code}</div>
			<p class="text-2xl font-bold text-primary mb-6">${data.product.price != null ? data.product.price.toFixed(2) : 'Contact for price'}</p>

			<div class="prose mb-8">
				<p>{data.product.description}</p>
			</div>
				
			<div class="flex flex-wrap gap-4 mb-8">
				<!-- Product Type Filter -->
				<div class="{data.product.product_type === 'single' ? 'hidden' : 'visible'}">
					<div class="form-control w-full lg:max-w-xs max-w-md">
						<label class="label">
							<span class="label-text">Size</span>
						</label>
						<select class="select select-bordered" bind:value={selectedSize}>
							<option disabled selected>Select size</option>
							<option>Small</option>
							<option>Medium</option>
							<option>Large</option>
						</select>
					</div>

					<div class="form-control w-full lg:max-w-xs max-w-md">
						<label class="label">
							<span class="label-text">Color</span>
						</label>
						<select class="select select-bordered" bind:value={selectedColor}>
							<option disabled selected>Select color</option>
							<option>Black</option>
							<option>White</option>
							<option>Green</option>
						</select>
					</div>
				</div>

				<div class="form-control w-full lg:max-w-xs max-w-md">
					<label class="label">
						<span>Quantity</span>
					</label>
					<input type="number" min="1" max="10" class="input" bind:value={quantity} />
				</div>

				<div class="form-control w-full lg:max-w-xs max-w-md">
					<button class="btn btn-primary w-full" on:click={() => addToCart()}> Add to Cart </button>
				</div>
			</div>
			
		</div>
	</div>
</div>

<!-- Image Zoom Modal -->
{#if showZoomModal}
	<div
		class="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
		on:click|self={closeModal}
	>
		<div class="relative max-w-4xl w-full max-h-[90vh]">
			<button class="btn btn-circle btn-sm absolute -top-10 right-0" on:click={closeModal}>
				✕
			</button>
			<img
				src={zoomImage}
				alt="Zoomed product image"
				class="w-full h-full object-contain max-h-[80vh]"
			/>
		</div>
	</div>
{/if}

<style>
	.prose {
		color: hsl(var(--bc) / 0.8);
	}
	.prose p {
		margin-bottom: 1rem;
	}
</style>
