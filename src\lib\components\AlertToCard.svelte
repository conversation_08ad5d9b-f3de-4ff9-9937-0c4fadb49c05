<script lang="ts">
export let name: string;
</script>
<div id="addToCartAlert" class="fixed bottom-4 left-1/2 -translate-x-1/2 z-50 pointer-events-none w-full sm:w-fit">
  <div class="alert alert-success shadow-lg pointer-events-auto">
    <div class="flex items-center gap-4">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 shrink-0 stroke-current" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
      <span>item added to cart successfully!</span>
    </div>
  </div>
</div>