<script lang="ts">
	export let order: {
		orderId: string;
		date: string;
		customer: {
			name: string;
			phone: string;
			address: string;
		};
		items: Array<{
			productId: string;
			title: string;
			price: number;
			quantity: number;
			size?: string;
			color?: string;
			image?: string;
		}>;
		total: number;
	};
</script>

<div class="bg-base-200 rounded-lg p-6 mb-6">
	<h2 class="text-xl font-bold mb-4">Order #{order.orderId}</h2>
	<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
		<div>
			<h3 class="font-semibold mb-2">Customer Information</h3>
			<div class="space-y-1">
				<p><span class="font-medium">Name:</span> {order.customer.name}</p>
				<p><span class="font-medium">Phone:</span> {order.customer.phone}</p>
				<p><span class="font-medium">Address:</span> {order.customer.address}</p>
				<p><span class="font-medium">Date:</span> {new Date(order.date).toLocaleString()}</p>
			</div>
		</div>

		<div>
			<h3 class="font-semibold mb-2">Order Summary</h3>
			<div class="space-y-4">
				{#each order.items as item}
					<div class="flex gap-4 items-start">
						{#if item.image}
							<img src={item.image} alt={item.title} class="w-16 h-16 object-cover rounded" />
						{:else}
							<div class="w-16 h-16 bg-base-300 rounded flex items-center justify-center">
								<span class="text-xs text-center">No Image</span>
							</div>
						{/if}
						<div class="flex-1">
							<p class="font-medium">{item.title}</p>
							<p class="text-sm text-base-content/70">
								{item.quantity} × ${item.price.toFixed(2)}
								{#if item.size || item.color}
									<br />
									{#if item.size}<span>Size: {item.size}</span>{/if}
									{#if item.color}<span>{item.size ? ' • ' : ''}Color: {item.color}</span>{/if}
								{/if}
							</p>
						</div>
						<p class="font-medium">
							${(item.price * item.quantity).toFixed(2)}
						</p>
					</div>
				{/each}

				<div class="border-t border-base-300 pt-4 space-y-2">
					<div class="flex justify-between">
						<span>Subtotal</span>
						<span>${order.total.toFixed(2)}</span>
					</div>
					<div class="flex justify-between">
						<span>Shipping</span>
						<span>$5.00</span>
					</div>
					<div class="flex justify-between font-bold text-lg">
						<span>Total</span>
						<span>${(order.total + 5).toFixed(2)}</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
