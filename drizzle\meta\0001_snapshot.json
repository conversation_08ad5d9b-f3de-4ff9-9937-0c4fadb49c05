{"version": "6", "dialect": "sqlite", "id": "39298178-437a-4b88-9e7d-fdecfb70b6ff", "prevId": "300e8cd5-4784-4477-9f94-84f14585981f", "tables": {"user": {"name": "user", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "age": {"name": "age", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_telegram": {"name": "user_telegram", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "telegramId": {"name": "telegramId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_telegram_userId_unique": {"name": "user_telegram_userId_unique", "columns": ["userId"], "isUnique": true}, "user_telegram_username_unique": {"name": "user_telegram_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}