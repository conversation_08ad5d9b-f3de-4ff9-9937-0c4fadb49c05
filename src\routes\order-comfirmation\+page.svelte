<script lang="ts">
	import { onMount } from 'svelte';
	import Swal from 'sweetalert2';
	import OrderSummary from '$lib/components/OrderSummary.svelte';

	let order: any = null;

	onMount(() => {
		// Get order from session storage
		if (typeof window !== 'undefined') {
			const savedOrder = sessionStorage.getItem('lastOrder');
			if (savedOrder) {
				order = JSON.parse(savedOrder);
				sessionStorage.removeItem('lastOrder');
			}
		}

		Swal.fire({
			title: 'Order Confirmed!',
			text: 'Thank you for your purchase.',
			icon: 'success',
			confirmButtonColor: '#1eb854'
		});
	});
</script>

<div class="container mx-auto px-4 py-8">
	<h1 class="text-3xl font-bold mb-6 text-center">Order Confirmation</h1>

	{#if order}
		<OrderSummary {order} />
		<div class="text-center mt-8">
			<p class="mb-4">
				We've sent a confirmation number <span class="font-semibold">{order.customer.phone}</span>
			</p>
			<a href="/" class="btn btn-primary">Continue Shopping</a>
		</div>
	{:else}
		<div class="alert alert-warning">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="stroke-current shrink-0 h-6 w-6"
				fill="none"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
				/>
			</svg>
			<span>Order details not found. Please check your phone sms for confirmation.</span>
		</div>
	{/if}
</div>
