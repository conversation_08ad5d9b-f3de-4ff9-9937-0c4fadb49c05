import { cart } from '$lib/stores/cart';
import Swal from 'sweetalert2';

export type CartItem = {
	id: string;
	title:string;
	name: string;
	price: number;
	type: string;
	image: string;
	description: string; // Added description property
	category: string;
	quantity: number;
	size?: string;
	color?: string;
};

export const CartService = {
	addItem: async (
		product: CartItem,
		options?: {
			size?: string;
			color?: string;
			quantity?: number;
		}
	) => {
		const { size, color, quantity = 1, } = options || {};

		// console.log(`Option: size ${size} | color ${color}`)

		if (!size || !color) {
			await Swal.fire({
				icon: 'error',
				title: 'Oops...',
				text: 'Please select size and color',
				confirmButtonColor: '#1eb854' // Forest theme primary color
			});
			return;
		}

		const itemId = `${product.id}${size ? `-${size}` : ''}${color ? `-${color}` : ''}`;

		cart.add(
			{
				id: itemId,
				title: `${product.name}${size ? ` (${size}${color ? `, ${color}` : ''})` : ''}`,
				price: product.price,
				image: product.image,
				category: product.category,
				description: product.description, // Added description property
				...(size && { size }),
				...(color && { color })
			},
			quantity
		);

		await Swal.fire({
			// position: 'top-end',
			icon: 'success',
			title: `${product.name} added to cart`,
			showConfirmButton: false,
			timer: 1500,
			backdrop: false,
			// background: 'var(--base-300)',
			color: 'var(--base-content)'
		});
	},

	removeItem: async (id: string) => {
		const result = await Swal.fire({
			title: 'Are you sure?',
			text: "You won't be able to revert this!",
			icon: 'warning',
			showCancelButton: true,
			confirmButtonColor: '#1eb854',
			cancelButtonColor: '#f87272',
			confirmButtonText: 'Yes, remove it!'
		});

		if (result.isConfirmed) {
			cart.remove(id);
			await Swal.fire({
				title: 'Removed!',
				text: 'Item has been removed from cart.',
				icon: 'success',
				confirmButtonColor: '#1eb854'
			});
		}
	},

	updateQuantity: async (id: string, quantity: number) => {
		if (quantity < 1) {
			await CartService.removeItem(id);
		} else {
			cart.update(id, quantity);
			await Swal.fire({
				position: 'top-end',
				icon: 'success',
				title: 'Quantity updated',
				showConfirmButton: false,
				timer: 1500,
				backdrop: false
			});
		}
	}
};
