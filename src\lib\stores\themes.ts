import { writable } from 'svelte/store';

export const theme = writable<'forest' | 'light'>('forest');

// Initialize theme from localStorage
if (typeof window !== 'undefined') {
	const savedTheme = localStorage.getItem('theme') as 'forest' | 'light' | null;
	theme.set(savedTheme || 'forest');

	// Update localStorage when theme changes
	theme.subscribe((value) => {
		localStorage.setItem('theme', value);
		document.documentElement.setAttribute('data-theme', value);
	});
}
