<script>
	import { cart } from '$lib/stores/cart';
	import { theme } from '$lib/stores/themes';
	import { FontAwesomeIcon } from '@fortawesome/svelte-fontawesome';
	import { faShoppingCart } from '@fortawesome/free-solid-svg-icons';
	import '$lib/app.postcss';

	function toggleTheme() {
		theme.update((current) => (current === 'forest' ? 'light' : 'forest'));
	}
</script>

<div class="min-h-screen bg-base-100" data-theme={$theme}>
	<!-- Navbar -->
	<div class="navbar bg-base-200 sticky top-0 z-50 shadow-lg">
		<div class="flex-1">
			<a href="/" class="btn btn-ghost text-xl gap-1">
				<span class="text-primary">Svelte</span>
				<span>Store</span>
			</a>
		</div>
		<div class="flex-none gap-2">
			<div class="indicator">
				<a href="/cart" class="btn btn-ghost">
					<FontAwesomeIcon icon={faShoppingCart} />
					<span class="badge badge-sm indicator-item badge-primary">{$cart.count}</span>
				</a>
			</div>
			<button class="btn btn-outline btn-sm" on:click={toggleTheme}>
				{$theme === 'forest' ? '🌲' : '☀️'}
			</button>
		</div>
	</div>

	<!-- Page Content -->
	<slot />
</div>
