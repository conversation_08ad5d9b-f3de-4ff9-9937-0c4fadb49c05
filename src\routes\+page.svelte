<script lang="ts">
	import { cart } from '$lib/stores/cart';
	import type { PageData } from './$types';
	import { FontAwesomeIcon } from '@fortawesome/svelte-fontawesome';
	import { faSearch } from '@fortawesome/free-solid-svg-icons';
	import type { CartItem } from '$lib/stores/cart';
	import AlertToCard from '$lib/components/AlertToCard.svelte';

	export let data: PageData;
	// console.log(data);

	let searchQuery = '';
	let selectedCategory = 'all';
	let addedCartedItem: string | null = null; // Store the product title
	$: showAlert = false;

	$: filteredProducts = data.products.filter((product) => {
		const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());
		const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
		return matchesSearch && matchesCategory;
	});

	function addToCart(product: CartItem) {
		cart.add({ ...product, id: product.id.toString() }, 1);
		addedToCartAlert(product.name);
	}

	function addedToCartAlert(productTitle: string) {
		addedCartedItem = productTitle;
		showAlert = true;
		// console.log('Added to cart:', addedCartedItem);
		setTimeout(() => {
			showAlert = false;
			addedCartedItem = null; // Clear the item after the alert
			// console.log('Reset to cart:', showAlert);
		}, 1500);
	}
</script>

<main class="container mx-auto px-4 py-8">
	<div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
		<h1 class="text-3xl font-bold">Nex Store Hot Picks</h1>

		<div class="flex flex-col md:flex-row gap-4 w-full md:w-auto">
			<!-- Search -->
			<label class="input input-bordered flex items-center gap-2">
				<FontAwesomeIcon icon={faSearch} class="w-4 h-4 opacity-70" />
				<input type="text" class="grow" placeholder="Search..." bind:value={searchQuery} />
			</label>

			<!-- Category Filter -->
			<select class="select select-bordered w-full md:w-48" bind:value={selectedCategory}>
				<option value="all">All Categories</option>
				{#each pro as category}
					<option value={category}>{category}</option>
				{/each}
			</select>
		</div>
	</div>

	{#if data.message === 'success' && data.products.length > 0}
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
			{#each filteredProducts as product}
				<div class="card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow">
					<a href="/product/{product.id}">
						<figure class="px-4 pt-4">
							<img src={product.image} alt={product.name} class="rounded-xl h-48 object-contain" />
						</figure>
					</a>
					<div class="card-body">
						<h2 class="card-title line-clamp-1">{product.name}</h2>
						<div class="badge badge-outline">{product.category}</div>
						<p class="text-gray-500 line-clamp-2">{product.description}</p>
						<div class="card-actions justify-between items-center mt-4">
							<span class="text-lg font-bold">${product.price != null ? product.price.toFixed(2) : 'Contact for price'}</span>
							<button on:click={() => addToCart(product)} class="btn btn-primary">
								Add to Cart
							</button>
						</div>
					</div>
				</div>
			{/each}
		</div>
		<!-- Alert component that appears when an item is added to cart -->
		{#if showAlert && addedCartedItem}
			<AlertToCard name={addedCartedItem} />
		{/if}
	{:else}
		<div class="text-center p-8 text-red-500">
			{data.message}
		</div>
	{/if}
</main>
