import { writable } from 'svelte/store';

export type CartItem = {
	id: string;
	name: string;
	price: number;
	code: string;
	image: string;
	description: string;
	category: string;
	quantity: number;
};

export type Cart = {
	items: CartItem[];
	count: number;
	total: number;
};

function createCart() {
	const { subscribe, update } = writable<Cart>({
		items: [],
		count: 0,
		total: 0
	});

	return {
		subscribe,
		add: (product: Omit<CartItem, 'quantity'>, quantity: number) => {
			update((cart) => {
				const existingItem = cart.items.find((item) => item.id === product.id);

				if (existingItem) {
					existingItem.quantity += quantity;
				} else {
					cart.items.push({ ...product, quantity });
				}

				cart.count = cart.items.reduce((sum, item) => sum + item.quantity, 0);
				cart.total = cart.items.reduce((sum, item) => sum + item.price * item.quantity, 0);

				return { ...cart };
			});
		},
		remove: (id: string) => {
			update((cart) => {
				cart.items = cart.items.filter((item) => item.id !== id);
				cart.count = cart.items.reduce((sum, item) => sum + item.quantity, 0);
				cart.total = cart.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
				return { ...cart };
			});
		},
		update: (id: string, quantity: number) => {
			update((cart) => {
				const item = cart.items.find((item) => item.id === id);
				if (item) {
					item.quantity = quantity;
					cart.count = cart.items.reduce((sum, item) => sum + item.quantity, 0);
					cart.total = cart.items.reduce((sum, item) => sum + item.price * item.quantity, 0);
				}
				return { ...cart };
			});
		},
		clear: () => {
			update(() => ({
				items: [],
				count: 0,
				total: 0
			}));
		}
	};
}

export const cart = createCart();
