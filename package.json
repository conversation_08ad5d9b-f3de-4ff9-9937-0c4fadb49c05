{"name": "latest", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "drizzle-kit": "drizzle-kit"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-netlify": "^5.0.0", "@sveltejs/adapter-node": "^5.2.11", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "autoprefixer": "^10.4.19", "daisyui": "^4.12.2", "drizzle-kit": "^0.30.6", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "postcss": "^8.4.35", "postcss-load-config": "^5.0.2", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.28.2", "svelte-check": "^4.0.0", "tailwindcss": "^3.4.3", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.0.0"}, "dependencies": {"@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/svelte-fontawesome": "^0.2.3", "@libsql/client": "^0.14.0", "@xom9ik/logger": "^0.7.8", "drizzle-orm": "^0.40.0", "sweetalert2": "^11.17.2", "telegraf": "^4.16.3", "vercel": "^41.6.2"}}