<script lang="ts">
	import { cart } from '$lib/stores/cart';
	import { FontAwesomeIcon } from '@fortawesome/svelte-fontawesome';
	import { faTrashAlt } from '@fortawesome/free-solid-svg-icons';

	function removeItem(id: string) {
		cart.remove(id);
	}

	function updateQuantity(id: string, quantity: number) {
		if (quantity > 0) {
			cart.update(id, quantity);
		} else {
			removeItem(id);
		}
	}
</script>

<main class="container mx-auto px-4 py-8 max-w-4xl">
	<h1 class="text-3xl font-bold mb-8">Your Cart</h1>

	{#if $cart.count === 0}
		<div class="alert alert-info">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				class="stroke-current shrink-0 w-6 h-6"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
				/>
			</svg>
			<span>Your cart is empty</span>
			<a href="/" class="btn btn-sm btn-primary ml-4">Continue Shopping</a>
		</div>
	{:else}
		<div class="overflow-x-auto">
			<table class="table">
				<thead>
					<tr>
						<th>Product</th>
						<th>Price</th>
						<th>Quantity</th>
						<th>Total</th>
						<th></th>
					</tr>
				</thead>
				<tbody>
					{#each $cart.items as item}
						<tr>
							<td>
								<div class="flex items-center gap-3">
									<div class="avatar">
										<div class="mask mask-squircle w-12 h-12">
											<img src={item.image} alt={item.name} />
										</div>
									</div>
									<div>
										<div class="font-bold">{item.name}</div>
										<div class="text-sm text-gray-500">{item.code}</div>
									</div>
								</div>
							</td>
							<td>${item.price.toFixed(2)}</td>
							<td>
								<input
									type="number"
									class="input input-bordered w-20"
									min="1"
									value={item.quantity}
									on:change={(e) => {
										if (e.target) {
											updateQuantity(item.id, parseInt((e.target as HTMLInputElement).value));
										}
									}}
								/>
							</td>
							<td>${(item.price * item.quantity).toFixed(2)}</td>
							<td>
								<button on:click={() => removeItem(item.id)} class="btn btn-error btn-sm">
									<FontAwesomeIcon icon={faTrashAlt} />
								</button>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>

		<div class="flex justify-end mt-8">
			<div class="card bg-base-200 w-96">
				<div class="card-body">
					<h2 class="card-title">Order Summary</h2>
					<div class="flex justify-between">
						<span>Subtotal</span>
						<span>${$cart.total.toFixed(2)}</span>
					</div>
					<div class="flex justify-between">
						<span>Shipping</span>
						<span>$5.00</span>
					</div>
					<div class="divider"></div>
					<div class="flex justify-between font-bold text-lg">
						<span>Total</span>
						<span>${($cart.total + 5).toFixed(2)}</span>
					</div>
					<div class="card-actions justify-end mt-4">
						<a href="/checkout" class="btn btn-primary w-full">Proceed to Checkout</a>
					</div>
				</div>
			</div>
		</div>
	{/if}
</main>
