<script lang="ts">
	import { cart } from '$lib/stores/cart';
	import { OrderService } from '$lib/services/order.service';
	import Swal from 'sweetalert2';
	import { FontAwesomeIcon } from '@fortawesome/svelte-fontawesome';
	import { faUser, faEnvelope, faCreditCard, faLock, faPhone } from '@fortawesome/free-solid-svg-icons';
	import { goto } from '$app/navigation';

	let formData = {
		name: '',
		phone: '',
		address: '',
		payment: ''
	};

	let isSubmitting: boolean = false;

	async function handleSubmit() {
		if (!formData.name || !formData.phone || !formData.address) {
			await Swal.fire({
				title: 'Incomplete Form',
				text: 'Please fill all required fields',
				icon: 'warning',
				confirmButtonColor: '#1eb854'
			});
			return;
		}

		isSubmitting = true;

		try {
			const orderData = {
				customer: {
					name: formData.name,
					phone: formData.phone,
					address: formData.address
				},
				items: $cart.items.map((item) => ({
					productId: item.id,
					quantity: item.quantity,
					price: item.price,
					...(item.size && { size: item.size }),
					...(item.color && { color: item.color })
				})),
				total: $cart.total
			};

			await OrderService.submitOrder(orderData);

			// Clear cart on success
			cart.clear();

			// Redirect to confirmation page
			await goto('/order-comfirmation');
		} finally {
			isSubmitting = false;
		}
	}
</script>

<main class="container mx-auto px-4 py-8 max-w-4xl">
	<h1 class="text-3xl font-bold mb-8">Checkout</h1>

	<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
		<!-- Customer Info -->
		<div class="bg-base-200 p-6 rounded-lg">
			<h2 class="text-xl font-bold mb-4">Customer Information</h2>
			<div class="space-y-4">
				<div class="form-control">
					<label class="label">
						<span class="label-text">Full Name</span>
					</label>
					<label class="input input-bordered flex items-center gap-2">
						<FontAwesomeIcon icon={faUser} class="w-4 h-4 opacity-70" />
						<input type="text" class="grow" bind:value={formData.name} placeholder="John Doe" />
					</label>
				</div>
				<div class="form-control">
					<label class="label">
						<span class="label-text">Phone Number</span>
					</label>
					<label class="input input-bordered flex items-center gap-2">
						<FontAwesomeIcon icon={faPhone} class="w-4 h-4 opacity-70" />
						<input
							type="tel"
							class="grow"
							bind:value={formData.phone}
							placeholder="****** 341 9876"
							required
							minlength={6}
							maxlength={10}
						/>
					</label>
				</div>
				<div class="form-control">
					<label class="label">
						<span class="label-text">Address</span>
					</label>
					<label class="input input-bordered flex items-center gap-2">
						<FontAwesomeIcon icon={faLock} class="w-4 h-4 opacity-70" />
						<input type="text" class="grow" bind:value={formData.address} placeholder="Toul Kork" />
					</label>
				</div>
			</div>
		</div>

		<!-- Payment -->
		<div class="bg-base-200 p-6 rounded-lg">
			<h2 class="text-xl font-bold mb-4">Payment Method</h2>
			<div class="space-y-4">
				<div class="form-control">
					<label class="label">
						<span class="label-text">Choose Method</span>
					</label>
					<label class="input join-item flex items-center gap-2">
						<FontAwesomeIcon icon={faCreditCard} class="w-4 h-4 opacity-70" />
						<select class="select join-item w-full" bind:value={formData.payment}>
							<option>Cash On delivery</option>
							<option>KHQR</option>
						</select>
					</label>
				</div>
			</div>
		</div>
	</div>

	<!-- Order Summary -->
	<div class="mt-8 bg-base-200 p-6 rounded-lg">
		<h2 class="text-xl font-bold mb-4">Order Summary</h2>
		<div class="space-y-4">
			{#each $cart.items as item}
				<div class="flex justify-between items-center">
					<div class="flex items-center gap-3">
						<div class="avatar">
							<div class="mask mask-squircle w-12 h-12">
								<img src={item.image} alt={item.title} />
							</div>
						</div>
						<div>
							<div class="font-bold">{item.title}</div>
							<div class="text-sm text-gray-500">Qty: {item.quantity}</div>
						</div>
					</div>
					<div class="font-bold">${(item.price * item.quantity).toFixed(2)}</div>
				</div>
			{/each}

			<div class="divider"></div>

			<div class="space-y-2">
				<div class="flex justify-between">
					<span>Subtotal</span>
					<span>${$cart.total.toFixed(2)}</span>
				</div>
				<div class="flex justify-between">
					<span>Shipping</span>
					<span>$5.00</span>
				</div>
				<div class="flex justify-between text-lg font-bold">
					<span>Total</span>
					<span>${($cart.total + 5).toFixed(2)}</span>
				</div>
			</div>

			<button class="btn btn-primary w-full mt-6" on:click={handleSubmit()}>Complete Order</button>
		</div>
	</div>
</main>
