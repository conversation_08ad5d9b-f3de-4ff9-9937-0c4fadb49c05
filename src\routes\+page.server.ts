import type { PageServerLoad } from './$types';
import type { CartItem } from '../lib/stores/cart';
import { STORE_URL, API_PREFIX } from '$env/static/private';
import type { ProductResponse } from '../lib/types';


export const load: PageServerLoad = async ({ fetch }) => {
    try {
        const uri = `${STORE_URL}${API_PREFIX}/product`;
        const response = await fetch(uri);
        console.log(`Fetch Data ->> ${uri}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result: ProductResponse = await response.json();

        if (!result.data?.length) {
            throw new Error('No products found');
        }

        // Transform the data to match the CartItem structure
        const products: CartItem[] = result.data.map(item => ({
            id: item.id.toString(),
            name: item.name,
            price: item.price,
            code: item.itemCode,
            description: item.description || '',
            image: item.image,
            category: item.category?.name || 'Uncategorized',
            quantity: 1
        }));

        return {
            message: 'success',
            products
        };
    } catch (error) {
        console.error('Failed to load products:', error);

        return {
            message: 'Failed to load products. Please try again later.',
            products: []
        };
    }
};
