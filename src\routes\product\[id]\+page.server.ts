import { STORE_URL, API_PREFIX } from '$env/static/private';

export const load = async ({ params }) => {
	// Fetch product data detail
	const productRes = await fetch(`${STORE_URL}${API_PREFIX}/products/${params.id}`);
	const rawproduct = await productRes.json();
	const product = await rawproduct.data
	// console.log('Get Product Detail:->>>',product.data)

	// Generate multiple images for demo (FakeStoreAPI only provides 1 image)
	const images = [
		product.image,
		`https://picsum.photos/800/800?random=${product.id}-1`,
		`https://picsum.photos/800/800?random=${product.id}-2`,
		`https://picsum.photos/800/800?random=${product.id}-3`
	];

	return {
		product: {
			...product,
			images
		}
	};
};
